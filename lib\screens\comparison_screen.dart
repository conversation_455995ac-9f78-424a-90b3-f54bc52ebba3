import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/product.dart';
import '../providers/comparison_provider.dart';
import '../providers/cart_provider.dart';
import 'product_detail_screen.dart';

class ComparisonScreen extends StatefulWidget {
  const ComparisonScreen({super.key});

  @override
  State<ComparisonScreen> createState() => _ComparisonScreenState();
}

class _ComparisonScreenState extends State<ComparisonScreen> {
  bool _showDetails = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '<PERSON>ry<PERSON> deňeşdirmek',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          Consumer<ComparisonProvider>(
            builder: (context, comparisonProvider, child) {
              return PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert),
                onSelected: (value) {
                  switch (value) {
                    case 'clear_all':
                      _showClearAllDialog(comparisonProvider);
                      break;
                    case 'show_report':
                      _showComparisonReport(comparisonProvider);
                      break;
                    case 'toggle_details':
                      setState(() {
                        _showDetails = !_showDetails;
                      });
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'toggle_details',
                    child: Row(
                      children: [
                        Icon(Icons.info_outline),
                        SizedBox(width: 8),
                        Text('Jikme-jik maglumat'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'show_report',
                    child: Row(
                      children: [
                        Icon(Icons.assessment),
                        SizedBox(width: 8),
                        Text('Hasabat görmek'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'clear_all',
                    child: Row(
                      children: [
                        Icon(Icons.clear_all),
                        SizedBox(width: 8),
                        Text('Ählisini arassalamak'),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer<ComparisonProvider>(
        builder: (context, comparisonProvider, child) {
          if (comparisonProvider.isEmpty) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              // Statistika
              _buildStatistics(comparisonProvider),
              
              // Deňeşdirme tablisasy
              Expanded(
                child: _buildComparisonTable(comparisonProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.compare_arrows,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Deňeşdirmek üçin haryt ýok',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Harytlary deňeşdirmek üçin haryt kartlaryndan deňeşdirme düwmesine basyň',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.shopping_bag),
            label: const Text('Harytlara gaýtmak'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatistics(ComparisonProvider provider) {
    final stats = provider.getComparisonStats();
    final recommended = provider.getRecommendedProduct();

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withValues(alpha: 0.1),
            Theme.of(context).primaryColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                'Haryt sany',
                '${stats['totalProducts']}',
                Icons.inventory,
                Colors.blue,
              ),
              _buildStatItem(
                'Orta baha',
                '\$${(stats['averagePrice'] as double).toStringAsFixed(2)}',
                Icons.attach_money,
                Colors.green,
              ),
              _buildStatItem(
                'Orta reýting',
                '${(stats['averageRating'] as double).toStringAsFixed(1)} ⭐',
                Icons.star,
                Colors.orange,
              ),
            ],
          ),
          
          if (recommended != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.recommend, color: Colors.green),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Maslahat: ${recommended.name}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ProductDetailScreen(product: recommended),
                        ),
                      );
                    },
                    child: const Text('Görmek'),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildComparisonTable(ComparisonProvider provider) {
    final products = provider.comparisonList;
    
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.all(16),
      child: DataTable(
        columnSpacing: 20,
        columns: [
          const DataColumn(
            label: Text(
              'Aýratynlyk',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          ...products.map((product) => DataColumn(
            label: SizedBox(
              width: 120,
              child: Column(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CachedNetworkImage(
                      imageUrl: product.imageUrls.first,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) => Container(
                        width: 60,
                        height: 60,
                        color: Colors.grey[300],
                        child: const Icon(Icons.image_not_supported),
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    product.name,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          )),
        ],
        rows: [
          _buildDataRow('Baha', products.map((p) => 
              '\$${(p.price - (p.price * p.discount / 100)).toStringAsFixed(2)}').toList()),
          if (_showDetails) ...[
            _buildDataRow('Asyl baha', products.map((p) => '\$${p.price.toStringAsFixed(2)}').toList()),
            _buildDataRow('Arzanladyş', products.map((p) => '${p.discount.toStringAsFixed(0)}%').toList()),
          ],
          _buildDataRow('Reýting', products.map((p) => '${p.rating.toStringAsFixed(1)} ⭐').toList()),
          if (_showDetails) ...[
            _buildDataRow('Reýting sany', products.map((p) => '${p.reviewCount}').toList()),
            _buildDataRow('Kategoriýa', products.map((p) => p.category).toList()),
          ],
          _buildDataRow('Hereket', products.map((p) => '').toList(), isAction: true, products: products),
        ],
      ),
    );
  }

  DataRow _buildDataRow(String feature, List<String> values, {bool isAction = false, List<Product>? products}) {
    return DataRow(
      cells: [
        DataCell(
          Text(
            feature,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        ...values.asMap().entries.map((entry) {
          final index = entry.key;
          final value = entry.value;
          
          if (isAction && products != null) {
            return DataCell(
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.shopping_cart, size: 16),
                    onPressed: () {
                      Provider.of<CartProvider>(context, listen: false)
                          .addItem(products[index]);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('${products[index].name} sebede goşuldy'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    },
                    tooltip: 'Sebede goş',
                  ),
                  IconButton(
                    icon: const Icon(Icons.remove_circle, size: 16, color: Colors.red),
                    onPressed: () {
                      Provider.of<ComparisonProvider>(context, listen: false)
                          .removeFromComparison(products[index].id);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('${products[index].name} deňeşdirmeden aýryldy'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    },
                    tooltip: 'Deňeşdirmeden aýyr',
                  ),
                ],
              ),
            );
          }
          
          return DataCell(
            Text(
              value,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          );
        }),
      ],
    );
  }

  void _showClearAllDialog(ComparisonProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ählisini arassalamak'),
        content: const Text('Deňeşdirme sanawyndan ähli harytlary aýyrmak isleýärsiňizmi?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Ýok'),
          ),
          TextButton(
            onPressed: () {
              provider.clearComparison();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Deňeşdirme sanawy arassalandy'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Hawa'),
          ),
        ],
      ),
    );
  }

  void _showComparisonReport(ComparisonProvider provider) {
    final report = provider.generateComparisonReport();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Deňeşdirme hasabaty'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Text(
              report,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Ýap'),
          ),
        ],
      ),
    );
  }
}
