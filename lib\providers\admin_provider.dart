import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/product.dart';

class AdminProvider with ChangeNotifier {
  bool _isLoggedIn = false;
  final String _adminPassword = "baglan2025"; // Admin paroly

  final List<Product> _products = [
    Product(
      id: '1',
      name: 'iPhone 15 Pro',
      description: 'Iň täze iPhone modeli, güýçli kamera we çalt prosessor bilen. Bu telefon häzirki zaman tehnologiýasynyň iň gowy mysalydyr.',
      price: 999.99,
      imageUrl: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400',
      imageUrls: [
        'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400',
        'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400',
        'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400',
      ],
      category: 'Elektronika',
      rating: 4.8,
      reviewCount: 1250,
      discount: 10,
    ),
    Product(
      id: '2',
      name: 'Samsung Galaxy S24',
      description: 'Güýçli Android telefon, ajaýyp ekran we uzak batareýa ömri',
      price: 899.99,
      imageUrl: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400',
      category: 'Elektronika',
      rating: 4.6,
      reviewCount: 890,
    ),
    Product(
      id: '3',
      name: 'MacBook Air M3',
      description: 'Ýeňil we güýçli noutbuk, uzak işleýän batareýa bilen',
      price: 1299.99,
      imageUrl: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400',
      category: 'Elektronika',
      rating: 4.9,
      reviewCount: 2100,
      discount: 5,
    ),
    Product(
      id: '4',
      name: 'Nike Air Max',
      description: 'Rahat sport aýakgap, gündelik geýim üçin amatly',
      price: 129.99,
      imageUrl: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400',
      category: 'Sport',
      rating: 4.4,
      reviewCount: 567,
    ),
    Product(
      id: '5',
      name: 'Türkmen Halysý',
      description: 'Asyl türkmen halysy, el bilen dokalýan',
      price: 299.99,
      imageUrl: 'https://images.unsplash.com/photo-1506439773649-6e0eb8cfb237?w=400',
      category: 'Öý üçin',
      rating: 4.9,
      reviewCount: 234,
      discount: 15,
    ),
    Product(
      id: '6',
      name: 'Türkmen Çaýy',
      description: 'Asyl türkmen çaýy, täze ýygnan ýapraklar',
      price: 25.99,
      imageUrl: 'https://images.unsplash.com/photo-**********-7f47ccb76574?w=400',
      category: 'Iýmit',
      rating: 4.7,
      reviewCount: 456,
    ),
  ];

  AdminProvider() {
    _loadProducts();
  }

  bool get isLoggedIn => _isLoggedIn;
  List<Product> get products => _products;
  
  bool login(String password) {
    if (password == _adminPassword) {
      _isLoggedIn = true;
      notifyListeners();
      return true;
    }
    return false;
  }
  
  void logout() {
    _isLoggedIn = false;
    notifyListeners();
  }
  
  // Harytlary ýüklemek
  Future<void> _loadProducts() async {
    try {
      print('💾 Harytlar ýüklenýär...');
      final prefs = await SharedPreferences.getInstance();
      final String? productsJson = prefs.getString('products');

      if (productsJson != null) {
        final List<dynamic> productsList = jsonDecode(productsJson);
        final List<Product> loadedProducts = productsList
            .map((json) => Product.fromJson(json))
            .toList();

        // Ýüklenen harytlary default harytlar bilen birleşdirmek
        final Set<String> existingIds = _products.map((p) => p.id).toSet();
        for (final product in loadedProducts) {
          if (!existingIds.contains(product.id)) {
            _products.add(product);
          }
        }

        print('💾 ${loadedProducts.length} haryt ýüklendi');
        print('💾 Jemi haryt sany: ${_products.length}');
        notifyListeners();
      } else {
        print('💾 Hiç haryt ýatda saklanmadyk');
      }
    } catch (e) {
      print('❌ Harytlary ýüklemekde ýalňyşlyk: $e');
    }
  }

  // Harytlary ýatda saklamak
  Future<void> _saveProducts() async {
    try {
      print('💾 Harytlar ýatda saklanýar...');
      final prefs = await SharedPreferences.getInstance();

      // Diňe täze goşulan harytlary saklamak (default harytlary däl)
      final customProducts = _products.where((product) {
        // Default harytlaryň ID-leri '1', '2', '3', '4', '5', '6' bolup durýar
        return !['1', '2', '3', '4', '5', '6'].contains(product.id);
      }).toList();

      final String productsJson = jsonEncode(
        customProducts.map((product) => product.toJson()).toList(),
      );

      await prefs.setString('products', productsJson);
      print('💾 ${customProducts.length} haryt ýatda saklandy');
    } catch (e) {
      print('❌ Harytlary ýatda saklamakda ýalňyşlyk: $e');
    }
  }

  void addProduct(Product product) {
    print('🛍️ Täze haryt goşulýar: ${product.name}');
    print('🛍️ Haryt ID: ${product.id}');
    print('🛍️ Haryt suraty: ${product.imageUrl.substring(0, 50)}...');
    print('🛍️ Haryt suratlar sany: ${product.imageUrls.length}');

    _products.add(product);
    print('🛍️ Haryt goşuldy. Jemi haryt sany: ${_products.length}');

    // Harytlary ýatda saklamak
    _saveProducts();

    notifyListeners();
    print('🛍️ Listeners habar berildi');
  }
  
  void updateProduct(String id, Product updatedProduct) {
    final index = _products.indexWhere((product) => product.id == id);
    if (index != -1) {
      _products[index] = updatedProduct;
      notifyListeners();
    }
  }
  
  void deleteProduct(String id) {
    _products.removeWhere((product) => product.id == id);
    notifyListeners();
  }
  
  Product? getProductById(String id) {
    try {
      return _products.firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }

  // Stok dolandyryş funksiýalary

  // Az galan harytlary almak
  List<Product> get lowStockProducts {
    return _products.where((product) => product.isLowStock && !product.isOutOfStock).toList();
  }

  // Gutaran harytlary almak
  List<Product> get outOfStockProducts {
    return _products.where((product) => product.isOutOfStock).toList();
  }

  // Stok täzelemek
  void updateStock(String productId, int newQuantity) {
    final productIndex = _products.indexWhere((product) => product.id == productId);
    if (productIndex != -1) {
      final product = _products[productIndex];
      final updatedProduct = Product(
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        imageUrl: product.imageUrl,
        imageUrls: product.imageUrls,
        category: product.category,
        rating: product.rating,
        reviewCount: product.reviewCount,
        isFavorite: product.isFavorite,
        discount: product.discount,
        stockQuantity: newQuantity,
        minStockLevel: product.minStockLevel,
      );
      _products[productIndex] = updatedProduct;
      notifyListeners();
    }
  }

  // Minimum stok derejesini üýtgetmek
  void updateMinStockLevel(String productId, int newMinLevel) {
    final productIndex = _products.indexWhere((product) => product.id == productId);
    if (productIndex != -1) {
      final product = _products[productIndex];
      final updatedProduct = Product(
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        imageUrl: product.imageUrl,
        imageUrls: product.imageUrls,
        category: product.category,
        rating: product.rating,
        reviewCount: product.reviewCount,
        isFavorite: product.isFavorite,
        discount: product.discount,
        stockQuantity: product.stockQuantity,
        minStockLevel: newMinLevel,
      );
      _products[productIndex] = updatedProduct;
      notifyListeners();
    }
  }

  // Haryt satylanda stok azaltmak
  void decreaseStock(String productId, int quantity) {
    final productIndex = _products.indexWhere((product) => product.id == productId);
    if (productIndex != -1) {
      final product = _products[productIndex];
      final newQuantity = (product.stockQuantity - quantity).clamp(0, double.infinity).toInt();
      updateStock(productId, newQuantity);
    }
  }

  // Haryt gelende stok artdyrmak
  void increaseStock(String productId, int quantity) {
    final productIndex = _products.indexWhere((product) => product.id == productId);
    if (productIndex != -1) {
      final product = _products[productIndex];
      final newQuantity = product.stockQuantity + quantity;
      updateStock(productId, newQuantity);
    }
  }

  // Stok duýduryşlary
  List<String> get stockAlerts {
    List<String> alerts = [];

    for (final product in outOfStockProducts) {
      alerts.add('${product.name} gutardy!');
    }

    for (final product in lowStockProducts) {
      alerts.add('${product.name} az galdy (${product.stockQuantity} sany)');
    }

    return alerts;
  }
}
